import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { LocalService } from './local-service';

/**
 * 代理模块配置
 */
export interface AgentConfig {
  repoPath?: string;          // 代码仓库路径
  enableRepoIndex?: boolean;  // 是否启用代码索引
  maxIndexSpace?: number;     // 最大索引空间(GB)
  proxyUrl?: string;          // 代理URL
  agentPreference?: string;   // 代理偏好设置
  userName?: string;          // 用户名
}

/**
 * 索引状态
 */
export interface IndexState {
  indexed: boolean;           // 是否已索引
  indexing: boolean;          // 是否正在索引
  indexingProgress: number;   // 索引进度(0-1)
  indexingMessage: string;    // 索引消息
  lastBuildTime: string;      // 上次构建时间
  pauseIndexManual: boolean;  // 是否手动暂停索引
  status: 'indexing' | 'indexed' | 'paused'; // 状态
}

/**
 * 代理服务 - 负责处理AI代理相关功能
 */
@Injectable()
export class AgentService implements OnModuleInit {
  private readonly logger = new Logger(AgentService.name);
  private config: AgentConfig = {
    enableRepoIndex: true,
    maxIndexSpace: 10,
    agentPreference: 'intelligent',
  };
  
  private indexState: IndexState = {
    indexed: false,
    indexing: false,
    indexingProgress: 0,
    indexingMessage: '',
    lastBuildTime: '',
    pauseIndexManual: false,
    status: 'paused',
  };

  constructor(private readonly localService: LocalService) {}

  async onModuleInit() {
    // 等待本地服务准备好
    this.localService.on('ready', () => {
      this.logger.log('本地服务准备就绪，初始化代理服务');
      this.setupEventHandlers();
      this.initAgent();
    });

    this.localService.on('restart', () => {
      this.logger.log('本地服务重启，重新初始化代理服务');
      this.setupEventHandlers();
      this.initAgent();
    });
  }

  /**
   * 设置事件处理器
   */
  private setupEventHandlers() {
    // 处理索引进度更新
    this.localService.onMessage('index/progress', (message) => {
      if (message.data) {
        this.updateIndexProgress(message.data);
      }
      return { status: 'ok' };
    });

    // 处理环境信息请求
    this.localService.onMessage('state/ideInfo', () => {
      return {
        status: 'ok',
        data: {
          pluginVersion: '1.0.0',
          version: '1.0.0',
          platform: 'deepsearch',
          device: 'deepsearch-server',
          repoInfo: {
            git_url: this.config.repoPath || '',
            dir_path: this.config.repoPath || '',
            commit: '',
            branch: '',
          },
          userInfo: {
            name: this.config.userName || 'deepsearch-user',
          },
          proxyUrl: this.config.proxyUrl || '',
          maxIndexSpace: this.config.maxIndexSpace || 10,
          cwd: process.cwd(),
        },
      };
    });

    // 处理配置请求
    this.localService.onMessage('config/getIdeSetting', () => {
      return {
        status: 'ok',
        data: {
          dirPath: this.config.repoPath || '',
          fileRetryTime: 3,
          modelRetryTime: 3,
          enableRepoIndex: this.config.enableRepoIndex,
          maxIndexSpace: this.config.maxIndexSpace,
          proxyUrl: this.config.proxyUrl || '',
          agentPreference: this.config.agentPreference,
        },
      };
    });
    // 处理状态请求
    this.localService.onMessage('state/ideState', () => {
      return { status: 'ok' };
    });
  }

  /**
   * 初始化代理服务
   */
  private async initAgent() {
    try {
      this.logger.log('初始化代理服务');
      
      // 检查仓库状态
      const res = await this.localService.request('state/checkRepoState', undefined);
      const shouldBuildIndex = this.config.enableRepoIndex && !res.data?.isPaused;
      
      if (shouldBuildIndex) {
        if (res.status === 'ok' && res.data?.progress === 1) {
          this.updateIndexProgress({
            progress: 1,
            indexed: true,
          });
          this.localService.sendMessage('index/build', undefined);
        } else {
          this.localService.sendMessage('index/repoIndex', undefined);
          this.indexState.indexing = true;
          this.indexState.pauseIndexManual = false;
          this.indexState.status = 'indexing';
        }
      } else {
        this.indexState.indexing = false;
        this.indexState.indexingProgress = 0;
        this.indexState.indexingMessage = '';
        this.indexState.lastBuildTime = '';
        this.indexState.pauseIndexManual = false;
        this.indexState.indexed = true;
        this.indexState.status = 'indexed';
      }
    } catch (error) {
      this.logger.error('初始化代理服务失败', error);
    }
  }

  /**
   * 更新索引进度
   */
  updateIndexProgress(data: { progress: number; indexed?: boolean }) {
    this.indexState.indexingProgress = data.progress;
    
    if (data.indexed !== undefined) {
      this.indexState.indexed = data.indexed;
    }
    
    if (data.progress >= 1) {
      this.indexState.indexing = false;
      this.indexState.indexed = true;
      this.indexState.status = 'indexed';
      this.indexState.lastBuildTime = new Date().toISOString();
      
      this.logger.log('索引构建完成');
    } else if (data.progress > 0) {
      this.indexState.indexing = true;
      this.indexState.status = 'indexing';
      
      this.logger.log(`索引构建进度: ${Math.floor(data.progress * 100)}%`);
    }
  }

  /**
   * 设置代理配置
   */
  setConfig(config: Partial<AgentConfig>) {
    this.config = { ...this.config, ...config };
    return this.config;
  }

  /**
   * 获取代理配置
   */
  getConfig(): AgentConfig {
    return this.config;
  }

  /**
   * 获取索引状态
   */
  getIndexState(): IndexState {
    return this.indexState;
  }

  /**
   * 执行文本搜索
   */
  async search(query: string, limit = 20) {
    try {
      const result = await this.localService.request('search/search', { query, limit });
      return result;
    } catch (error) {
      this.logger.error(`搜索失败: ${query}`, error);
      throw error;
    }
  }

  /**
   * 开始构建索引
   */
  startRepoIndex() {
    this.localService.sendMessage('index/repoIndex', undefined);
    this.indexState.indexing = true;
    this.indexState.pauseIndexManual = false;
    this.indexState.status = 'indexing';
    return this.indexState;
  }

  /**
   * 清除索引
   */
  clearIndex() {
    this.localService.sendMessage('index/clearIndex', undefined);
    this.indexState.indexed = false;
    this.indexState.indexing = false;
    this.indexState.indexingProgress = 0;
    this.indexState.status = 'paused';
    return this.indexState;
  }

  /**
   * 检查代理状态
   */
  async checkAgentState() {
    try {
      return await this.localService.request('state/agentState', undefined);
    } catch (error) {
      this.logger.error('检查代理状态失败', error);
      throw error;
    }
  }
}
