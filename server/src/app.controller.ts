import { Controller, Get, Post, Body, Query, Param, HttpStatus, HttpException } from '@nestjs/common';
import { AppService } from './app.service';
import { ComposerService } from './core';
import {
  ApiRequests,
  ApiResponses,
} from '@deepsearch/shared';

@Controller()
export class AppController {
  constructor(
    private readonly appService: AppService,
    private readonly composerService: ComposerService,
  ) { }

  @Get()
  getHello(): string {
    return 'Deepsearch API is running';
  }

  // 简单问答接口
  @Post('ask')
  async ask(@Body() req: ApiRequests.Question): Promise<ApiResponses.Answer> {
    if (!req.question || typeof req.question !== 'string') {
      throw new HttpException('无效的请求格式，缺少问题内容', HttpStatus.BAD_REQUEST);
    }

    try {
      const answer = await this.appService.answerQuestion(req.question);
      return { answer };
    } catch (error) {
      const errorMessage = (error instanceof Error) ? error.message : String(error);
      throw new HttpException(`处理问题失败: ${errorMessage}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // 创建新会话
  @Post('sessions')
  async createSession(@Body() req: ApiRequests.CreateSession): Promise<ApiResponses.SessionCreated> {
    try {
      const session = this.composerService.createSession({
        model: req.model,
        systemPrompt: req.systemPrompt,
      });

      if (req.name) {
        // 如果提供了名称，更新会话名称
        session.name = req.name;
      }

      return {
        id: session.id,
        name: session.name || `对话 ${new Date().toLocaleDateString()}`,
        createdAt: session.createdAt,
      };
    } catch (error) {
      const errorMessage = (error instanceof Error) ? error.message : String(error);
      throw new HttpException(`创建会话失败: ${errorMessage}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // 获取所有会话
  @Get('sessions')
  async getSessions() {
    try {
      const sessions = this.composerService.getAllSessions();
      return sessions.map(session => ({
        id: session.id,
        name: session.name || `对话 ${new Date(session.createdAt).toLocaleDateString()}`,
        createdAt: session.createdAt,
        updatedAt: session.updatedAt,
        messageCount: session.messages.filter(msg => msg.role !== 'system').length,
      }));
    } catch (error) {
      const errorMessage = (error instanceof Error) ? error.message : String(error);
      throw new HttpException(`获取会话失败: ${errorMessage}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // 获取指定会话
  @Get('sessions/:id')
  async getSession(@Param('id') id: string) {
    try {
      const session = this.composerService.getSession(id);
      if (!session) {
        throw new HttpException(`会话不存在: ${id}`, HttpStatus.NOT_FOUND);
      }

      return {
        id: session.id,
        name: session.name || `对话 ${new Date(session.createdAt).toLocaleDateString()}`,
        messages: session.messages.filter(msg => msg.role !== 'system'),
        config: session.config,
        createdAt: session.createdAt,
        updatedAt: session.updatedAt,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      const errorMessage = (error instanceof Error) ? error.message : String(error);
      throw new HttpException(`获取会话失败: ${errorMessage}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // 发送消息到指定会话
  @Post('sessions/:id/messages')
  async sendMessage(
    @Param('id') id: string,
    @Body() req: ApiRequests.SendMessage
  ): Promise<ApiResponses.MessageResponse> {
    try {
      if (!req.content || typeof req.content !== 'string') {
        throw new HttpException('无效的请求格式，缺少消息内容', HttpStatus.BAD_REQUEST);
      }

      // 检查会话是否存在
      const session = this.composerService.getSession(id);
      if (!session) {
        throw new HttpException(`会话不存在: ${id}`, HttpStatus.NOT_FOUND);
      }

      const reply = await this.composerService.sendMessage(id, req.content);

      return {
        id: reply.id || '',
        content: reply.content,
        role: reply.role,
        createdAt: reply.createdAt || Date.now(),
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      const errorMessage = (error instanceof Error) ? error.message : String(error);
      throw new HttpException(`发送消息失败: ${errorMessage}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // 删除会话
  @Post('sessions/:id/delete')
  async deleteSession(@Param('id') id: string) {
    try {
      const success = this.composerService.deleteSession(id);
      if (!success) {
        throw new HttpException(`会话不存在: ${id}`, HttpStatus.NOT_FOUND);
      }

      return { success: true };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      const errorMessage = (error instanceof Error) ? error.message : String(error);
      throw new HttpException(`删除会话失败: ${errorMessage}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // 健康检查接口
  @Get('health')
  async healthCheck() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    };
  }
}
