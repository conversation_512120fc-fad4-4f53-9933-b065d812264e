{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "es2017", "sourceMap": true, "outDir": "dist", "rootDir": "./src", "baseUrl": "./src", "incremental": true, "strict": true, "paths": {"@deepsearch/shared": ["../../packages/shared/src"]}}, "include": ["src/**/*"], "exclude": ["node_modules"]}