import React, { useState, useRef, useEffect } from 'react';
import './App.css';
interface Message {
  id: string;
  content: string;
  role: 'user' | 'assistant' | 'system';
  createdAt: number;
}

interface SessionSummary {
  id: string;
  name: string;
  messageCount: number;
  createdAt: number;
  updatedAt: number;
}

const API_URL = 'http://localhost:3001';

// 简单的文本截断函数
const truncateText = (text: string, maxLength = 50): string => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

export default function App() {
  const [question, setQuestion] = useState('');
  const [loading, setLoading] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [sessions, setSessions] = useState<SessionSummary[]>([]);
  const [currentSessionId, setCurrentSessionId] = useState<string>('');
  const [showSessionList, setShowSessionList] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // 获取会话列表
  const fetchSessions = async () => {
    try {
      const response = await fetch(`${API_URL}/sessions`);
      const data = await response.json();
      setSessions(data);
    } catch (error) {
      console.error('获取会话失败', error);
    }
  };

  // 创建新会话
  const createNewSession = async () => {
    try {
      const response = await fetch(`${API_URL}/sessions`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: '新对话 ' + new Date().toLocaleString()
        }),
      });
      const session = await response.json();
      setCurrentSessionId(session.id);
      setMessages([]);
      await fetchSessions();
    } catch (error) {
      console.error('创建会话失败', error);
    }
  };

  // 切换会话
  const switchSession = async (sessionId: string) => {
    setCurrentSessionId(sessionId);
    setShowSessionList(false);
    try {
      const response = await fetch(`${API_URL}/sessions/${sessionId}`);
      const data = await response.json();
      setMessages(data.messages || []);
    } catch (error) {
      console.error('获取会话消息失败', error);
    }
  };

  // 发送消息
  const handleSendMessage = async () => {
    if (!question.trim()) return;

    // 如果没有当前会话，创建一个新会话
    if (!currentSessionId) {
      try {
        const response = await fetch(`${API_URL}/sessions`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({}),
        });
        const newSession = await response.json();
        setCurrentSessionId(newSession.id);
        await fetchSessions();
      } catch (error) {
        console.error('创建会话失败', error);
        return;
      }
    }

    setLoading(true);
    
    // 先添加用户消息到UI
    const userMessage: Message = {
      id: 'temp-' + Date.now(),
      content: question,
      role: 'user',
      createdAt: Date.now(),
    };
    
    setMessages((prev) => [...prev, userMessage]);
    setQuestion('');

    try {
      const response = await fetch(`${API_URL}/sessions/${currentSessionId}/messages`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ content: question }),
      });
      
      const data = await response.json();
      
      // 添加助手回复
      setMessages((prev) => [...prev, data]);
      
      // 刷新会话列表
      fetchSessions();
    } catch (error) {
      console.error('发送消息失败', error);
      setMessages((prev) => [
        ...prev,
        {
          id: 'error-' + Date.now(),
          content: '发送消息失败，请稍后重试。',
          role: 'assistant',
          createdAt: Date.now(),
        },
      ]);
    } finally {
      setLoading(false);
    }
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // 自动调整textarea高度
  const adjustTextareaHeight = () => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = 
        Math.min(textareaRef.current.scrollHeight, 150) + 'px';
    }
  };

  // 滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // 首次加载
  useEffect(() => {
    fetchSessions();
  }, []);

  // 消息变化时滚动到底部
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // 输入变化时调整高度
  useEffect(() => {
    adjustTextareaHeight();
  }, [question]);

  return (
    <div className="app-container">
      <div className="sidebar">
        <div className="sidebar-header">
          <h2>DeepWiki</h2>
        </div>
        <button className="new-chat-btn" onClick={createNewSession}>
          + 新对话
        </button>
        <div className="session-list">
          {sessions.map((session) => (
            <div 
              key={session.id}
              className={`session-item ${currentSessionId === session.id ? 'active' : ''}`}
              onClick={() => switchSession(session.id)}
            >
              <span className="session-name">{truncateText(session.name, 30)}</span>
              <span className="session-count">{session.messageCount}条消息</span>
            </div>
          ))}
        </div>
      </div>

      <div className="main-content">
        <div className="chat-container">
          <div className="messages-container">
            {messages.length === 0 ? (
              <div className="welcome-container">
                <h1>DeepWiki 问答</h1>
                <p>有问题尽管问我，我会尽力帮助你！</p>
              </div>
            ) : (
              messages.map((message) => (
                <div 
                  key={message.id} 
                  className={`message ${message.role === 'user' ? 'user-message' : 'assistant-message'}`}
                >
                  <div className="message-content">{message.content}</div>
                </div>
              ))
            )}
            <div ref={messagesEndRef} />
          </div>

          <div className="input-container">
            <textarea
              ref={textareaRef}
              rows={1}
              value={question}
              onChange={(e) => setQuestion(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="输入问题，按Enter发送..."
              disabled={loading}
              className="question-input"
            />
            <button 
              onClick={handleSendMessage} 
              disabled={loading || !question.trim()} 
              className="send-button"
            >
              {loading ? '发送中...' : '发送'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
