#!/bin/bash
set -e  # 遇到错误立即退出

# 设置变量
DOWNLOAD_URL="https://cdnfile.corp.kuaishou.com/kc/files/a/kwaipilot/kwaipilot-binary/kwaipilot-binary-1.0.21.tar.gz"
TEMP_DIR="./temp_download"
TARGET_DIR="bin/agent/${os}-${arch}"
BINARY_NAME="kwaipilot-binary"
TAR_FILE="kwaipilot-binary-1.0.21.tar.gz"

# 显示彩色输出的函数
function echo_info() {
  echo -e "\033[1;34m[INFO]\033[0m $1"
}

function echo_success() {
  echo -e "\033[1;32m[SUCCESS]\033[0m $1"
}

function echo_error() {
  echo -e "\033[1;31m[ERROR]\033[0m $1"
}

function cleanup() {
  echo_info "清理临时文件..."
  rm -rf "$TEMP_DIR"
}

# 出错时清理
trap cleanup ERR

echo_info "开始安装 kwaipilot-binary..."

# 检测是否有旧的下载临时文件并清理
if [ -d "$TEMP_DIR" ]; then
  echo_info "发现旧的临时目录，正在清理..."
  rm -rf "$TEMP_DIR"
fi
# 创建临时目录和目标目录
mkdir -p "$TEMP_DIR"
mkdir -p "$TARGET_DIR"

# 下载文件
echo_info "正在下载 $DOWNLOAD_URL..."
echo_info "这可能需要几分钟时间，取决于您的网络速度..."

# 使用 curl 下载，增加错误处理和重试
MAX_RETRIES=3
RETRY_COUNT=0
DOWNLOAD_SUCCESS=false

while [ $RETRY_COUNT -lt $MAX_RETRIES ] && [ "$DOWNLOAD_SUCCESS" = false ]; do
  if curl -L --fail --connect-timeout 30 --retry 5 --retry-delay 10 --retry-max-time 120 "$DOWNLOAD_URL" -o "$TEMP_DIR/$TAR_FILE"; then
    DOWNLOAD_SUCCESS=true
    echo_success "下载完成!"
  else
    RETRY_COUNT=$((RETRY_COUNT + 1))
    if [ $RETRY_COUNT -lt $MAX_RETRIES ]; then
      echo_error "下载失败，正在重试 ($RETRY_COUNT/$MAX_RETRIES)..."
      sleep 5
    else
      echo_error "下载尝试次数过多，放弃下载。"
      cleanup
      exit 1
    fi
  fi
done

# 解压文件
echo_info "正在解压文件..."
tar -xzf "$TEMP_DIR/$TAR_FILE" -C "$TEMP_DIR" || {
    echo_error "解压失败!"
    cleanup
    exit 1
}

# 获取平台标识符函数
# 检测操作系统和架构
OS=$(uname -s | tr '[:upper:]' '[:lower:]')
ARCH=$(uname -m)

echo_info "检测到系统: $OS $ARCH"

# 确定目录名称
if [[ "$OS" == "darwin" ]]; then
    if [[ "$ARCH" == "x86_64" ]]; then
        SUB_DIR="darwin-x64"
    elif [[ "$ARCH" == "arm64" ]]; then
        SUB_DIR="darwin-arm64"
    fi
elif [[ "$OS" == "linux" ]]; then
    if [[ "$ARCH" == "x86_64" ]]; then
        SUB_DIR="linux-x64"
    elif [[ "$ARCH" == "aarch64" || "$ARCH" == "arm64" ]]; then
        SUB_DIR="linux-arm64"
    fi
fi

# 检查是否找到了匹配的子目录
if [ -z "$SUB_DIR" ]; then
    echo_error "无法识别您的操作系统或架构: $OS $ARCH"
    
    # 列出所有可能的目录选项
    echo_info "可用的子目录有:"
    find "$TEMP_DIR" -maxdepth 3 -type d | grep -v "^$TEMP_DIR$" | sort
    
    # 请用户选择
    echo_info "请手动输入适合您系统的子目录名称 (例如 darwin-x64):"
    read SUB_DIR
    
    if [ -z "$SUB_DIR" ]; then
        echo_error "未提供子目录，退出安装"
        cleanup
        exit 1
    fi
fi

echo_info "将使用 $SUB_DIR 目录下的二进制文件"

# 查找二进制文件
echo_info "正在查找二进制文件..."
BINARY_PATH=""

# 首先尝试精确匹配
BINARY_PATH=$(find "$TEMP_DIR" -path "*/$SUB_DIR/*$BINARY_NAME" -type f -perm -u+x 2>/dev/null | head -1)

# 如果没找到，尝试模糊匹配
if [ -z "$BINARY_PATH" ]; then
    BINARY_PATH=$(find "$TEMP_DIR" -path "*/$SUB_DIR/*" -type f -perm -u+x 2>/dev/null | head -1)
fi

# 如果仍然没找到，则列出所有可能的文件
if [ -z "$BINARY_PATH" ]; then
    echo_error "无法在 $SUB_DIR 目录下找到二进制文件"
    echo_info "解压后的文件结构:"
    find "$TEMP_DIR" -type f -perm -u+x | sort
    
    echo_info "请手动输入二进制文件的完整路径:"
    read BINARY_PATH
    
    if [ -z "$BINARY_PATH" ] || [ ! -f "$BINARY_PATH" ]; then
        echo_error "无效的文件路径，退出安装"
        cleanup
        exit 1
    fi
else
    echo_success "找到二进制文件: $BINARY_PATH"
fi

# 复制二进制文件到目标位置
echo_info "正在安装二进制文件到 $TARGET_DIR/$BINARY_NAME..."
cp "$BINARY_PATH" "$TARGET_DIR/$BINARY_NAME" || {
    echo_error "复制二进制文件失败!"
    cleanup
    exit 1
}

# 确保二进制文件有执行权限
chmod +x "$TARGET_DIR/$BINARY_NAME" || {
    echo_error "设置执行权限失败!"
    cleanup
    exit 1
}

# 清理临时文件
cleanup

echo_success "安装完成! 二进制文件位于: $TARGET_DIR/$BINARY_NAME"
ls -la "$TARGET_DIR/$BINARY_NAME"
