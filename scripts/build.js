// NOTE: 用于插件 产物编译，修改时需要注意是否同步./build-export.js
const { execSync } = require("child_process");
const parseArgs = require("minimist");
const fs = require("fs");
const path = require("path");
const os = require("os");

const args = process.argv.slice(2);
var argv = parseArgs(args, {
  string: ["target"],
});

const isPreRelease = args.includes("--pre-release");

const preReleaseFlag = isPreRelease ? " --pre-release" : "";
const curPlat = process.platform;
const curArch = process.arch;

const PLATFORMS = [
  `${curPlat}-${curArch}`,
  // "win32-x64",
  // // "win32-arm64",
  // "linux-x64",
  // "linux-arm64",
  // "darwin-x64",
  // "darwin-arm64",
];

/**
 * 检查目录是否存在，不存在则报错
 * @param {string} dirPath 要检查的目录路径
 * @param {string} errorMessage 错误消息
 */
function ensureDirectoryExists(dirPath, errorMessage) {
  if (!fs.existsSync(dirPath)) {
    console.error(`❌ 错误: ${errorMessage || "目录不存在"}: ${dirPath}`);
    process.exit(1);
  }
}

/**
 * 获取当前系统的平台标识
 * @returns {string} 平台标识，例如 "darwin-arm64"
 */
function getCurrentPlatform() {
  const platform = os.platform();
  const arch = os.arch();
  const platformMap = {
    darwin: "darwin",
    win32: "win32",
    linux: "linux",
  };

  const archMap = {
    x64: "x64",
    arm64: "arm64",
  };

  const normalizedPlatform = platformMap[platform] || platform;
  const normalizedArch = archMap[arch] || arch;

  return `${normalizedPlatform}-${normalizedArch}`;
}

const platformsFromArgs = (
  Array.isArray(argv.target) ? argv.target : [argv.target]
).filter((v) => PLATFORMS.includes(v));

const finalPlatforms =
  platformsFromArgs.length > 0 ? platformsFromArgs : PLATFORMS;

console.log("building for platforms:", finalPlatforms);

(async () => {
  for (const platform of finalPlatforms) {
    console.log("building vsix for platform:", platform);

    // 创建必要的目录
    execSync(`mkdir -p ./build/Release`);
    execSync(`mkdir -p ./local-agent`);

    // 检查源目录是否存在
    const agentSourceDir = `./bin/agent/${platform}`;
    ensureDirectoryExists(agentSourceDir, `缺少平台 ${platform} 的 agent 目录`);

    // 复制对应平台的所有文件到 local-agent
    execSync(`cp -r ${agentSourceDir}/* ./local-agent/`);

    // 检查 sqlite3 目录是否存在
    const sqlite3SourceDir = `./bin/sqlite3/${platform}/Release`;
    ensureDirectoryExists(
      sqlite3SourceDir,
      `缺少平台 ${platform} 的 sqlite3 目录`
    );
    ensureDirectoryExists(
      `${sqlite3SourceDir}/node_sqlite3.node`,
      `缺少平台 ${platform} 的 sqlite3 模块`
    );

    // 复制 sqlite3
    execSync(
      `cp ${sqlite3SourceDir}/node_sqlite3.node ./build/Release/node_sqlite3.node`
    );

    const pkgCommand = `node ./scripts/package.js${preReleaseFlag} --target ${platform}`;

    execSync(pkgCommand, { stdio: "inherit" });

    // 清理文件
    execSync("rm ./build/Release/node_sqlite3.node");
    execSync("rm -rf ./local-agent/*"); // 使用 rm -rf 来清理目录下的所有内容
  }

  // 构建完成后，复制当前平台的内容
  const currentPlatform = getCurrentPlatform();
  if (finalPlatforms.includes(currentPlatform)) {
    console.log(`\n✅ 正在为当前系统 (${currentPlatform}) 准备开发环境...`);

    // 源目录和目标目录，根据实际项目结构调整
    const agentSourceDir = `./bin/agent/${currentPlatform}`;
    execSync(`cp -r ${agentSourceDir}/* ./local-agent/`);
    const sqlite3SourceDir = `./bin/sqlite3/${currentPlatform}/Release`;
    execSync(
      `cp ${sqlite3SourceDir}/node_sqlite3.node ./build/Release/node_sqlite3.node`
    );

    console.log(`✅ 已为当前平台 (${currentPlatform}) 准备好开发环境`);
  } else {
    console.log(
      `\n⚠️ 警告: 当前平台 (${currentPlatform}) 不在构建列表中，跳过准备开发环境步骤`
    );
  }

  console.log("\n✅ 构建完成!");
})().catch((err) => {
  console.error("构建过程中出错:", err);
  process.exit(1);
});
