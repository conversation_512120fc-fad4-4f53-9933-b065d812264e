/**
 * DeepSearch 共享类型和工具函数
 */

// 消息类型定义
export interface Message {
  id: string;
  content: string;
  role: 'user' | 'assistant' | 'system';
  createdAt: number;
}

// 会话配置
export interface SessionConfig {
  model?: string;
  temperature?: number;
  contextItems?: string[];
  systemPrompt?: string;
}

// 会话数据
export interface Session {
  id: string;
  name?: string;
  messages: Message[];
  config: SessionConfig;
  createdAt: number;
  updatedAt: number;
}

// 会话摘要 (用于列表显示)
export interface SessionSummary {
  id: string;
  name: string;
  createdAt: number;
  updatedAt: number;
  messageCount: number;
}

// API 请求类型
export namespace ApiRequests {
  // 问题请求接口
  export interface Question {
    question: string;
  }
  
  // 会话创建请求
  export interface CreateSession {
    name?: string;
    model?: string;
    systemPrompt?: string;
  }
  
  // 消息发送请求
  export interface SendMessage {
    content: string;
  }
}

// API 响应类型
export namespace ApiResponses {
  // 问题回答响应
  export interface Answer {
    answer: string;
  }
  
  // 创建会话响应
  export interface SessionCreated {
    id: string;
    name: string;
    createdAt: number;
  }
  
  // 消息回复响应
  export interface MessageResponse {
    id: string;
    content: string;
    role: string;
    createdAt: number;
  }
}

// 工具函数
export const formatDate = (timestamp: number): string => {
  return new Date(timestamp).toLocaleString();
};

export const truncateText = (text: string, maxLength: number = 50): string => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};
