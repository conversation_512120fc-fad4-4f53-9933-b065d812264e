{
  "compilerOptions": {
    "target": "es2019", // Updated to be compatible with Node.js v16
    "module": "commonjs", // Changed from esnext to commonjs for Node.js compatibility
    "declaration": true,
    "outDir": "./dist",
    "baseUrl": "./src",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "composite": true, // Added to support project references
    // "isolatedModules": true, // Removed as it is not necessary for commonjs modules
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist"]
}